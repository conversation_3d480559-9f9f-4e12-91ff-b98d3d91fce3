# 使用基础镜像（需包含Supervisord）
FROM harbor.baocloud.cn/bwty-gyck-xc-jygx-eplat-test/centos:7
#FROM centos:7
# 创建目录结构
RUN mkdir -p \
    /etc/supervisord.d \
    /var/log/supervisor \
    /opt/apisix \
    /opt/etcd \
    /opt/apisix/rpm \
    /opt/apisix-dashboard \
    /opt/apisix/conf \
    /opt/etcd/conf \
    /opt/apisix-dashboard/conf \
    /opt/apisix-dashboard/rpm \
    /opt/casdoor \
    /opt/casdoor/log \
    /opt/casdoor/conf \
    /opt/casdoor/web \
    /opt/casdoor/rpm \
    /opt/nginx/run \
    /opt/dashboard \
    /opt/dashboard/log \
    /opt/dashboard/logs

# ----------------------------
# 1. 安装 ETCD
# ----------------------------
# 下载ETCD二进制包（替换为最新版本URL）
COPY etcd/etcd-v3.5.11-linux-amd64.tar.gz /tmp/etcd-v3.5.11-linux-amd64.tar.gz
RUN cd /tmp && \
    tar -zxvf /tmp/etcd-v3.5.11-linux-amd64.tar.gz && \
    mv etcd-v3.5.11-linux-amd64/etcd* /usr/local/bin/ && \
    rm -rf /tmp/etcd-v3.5.11-linux-amd64.tar.gz
# 复制ETCD配置文件
COPY config/etcd.yaml /opt/etcd/conf/etcd.yaml
COPY config/dm_svc.conf /etc/dm_svc.conf
# ----------------------------
# 2. 安装 APISIX
# ----------------------------
# 从你的私有仓库拉取APISIX镜像（或直接安装二进制）


# 假设你已经将APISIX二进制文件打包到镜像中
# 或使用官方Docker镜像中的二进制（需调整路径）
# 这里以直接使用官方镜像中的文件为例（需确保兼容性）
#COPY --from=apisix:3.8.0-local /usr/local/apisix /usr/local/apisix
COPY apisix/* /opt/apisix/rpm/
RUN rpm -Uvh /opt/apisix/rpm/*.rpm --nodeps --force && \
    apisix init
COPY config/config.yaml /usr/local/apisix/conf/config.yaml
# ----------------------------
# 3. 安装 APISIX Dashboard
# ----------------------------
# 同理，复制Dashboard二进制或使用官方镜像中的文件

#COPY dashboard/* /opt/apisix-dashboard/rpm/
#RUN yum install -y /opt/apisix-dashboard/rpm/apisix-dashboard-2.9.0-0.el7.x86_64.rpm
#COPY config/conf.yaml /usr/local/apisix/dashboard/conf/conf.yaml

COPY dashboard/main /opt/dashboard/main
COPY dashboard/conf/* /opt/dashboard/conf/
COPY dashboard/html.zip /opt/dashboard/web/html.zip
RUN unzip /opt/dashboard/web/html.zip -d /opt/dashboard/web/build && \
    rm -rf /opt/dashboard/web/html.zip && \
    chmod +x /opt/dashboard/main


COPY casdoor/main /opt/casdoor/casdoor
COPY casdoor/conf/* /opt/casdoor/conf/
COPY casdoor/web/build.zip /opt/casdoor/web/build.zip
RUN unzip /opt/casdoor/web/build.zip -d /opt/casdoor/web&& \
    rm -rf /opt/casdoor/web/build.zip && \
    chmod +x /opt/casdoor/casdoor


COPY casdoor/web/nginx/* /opt/casdoor/rpm/
RUN rpm -Uvh /opt/casdoor/rpm/*.rpm --nodeps --force
COPY casdoor/web/nginx.conf /etc/nginx/conf.d/default.conf

# ----------------------------
# 配置 Supervisord
# ----------------------------
COPY supervisord/setuptools-41.1.0.tar.gz /tmp/setuptools-41.1.0.tar.gz
RUN tar -zxvf /tmp/setuptools-41.1.0.tar.gz -C /usr/local && \
    cd /usr/local/setuptools-41.1.0 && \
    python setup.py install && \
    rm -rf /tmp/setuptools-41.1.0.zip

COPY supervisord/meld3-1.0.2.tar.gz /tmp/meld3-1.0.2.tar.gz
RUN tar -zxvf /tmp/meld3-1.0.2.tar.gz -C /usr/local && \
    cd /usr/local/meld3-1.0.2 && \
    python setup.py install && \
    rm -rf /tmp/meld3-1.0.2.tar.gz

COPY supervisord/supervisor-4.0.4.tar.gz /tmp/supervisor-4.0.4.tar.gz
RUN tar -zxvf /tmp/supervisor-4.0.4.tar.gz -C /usr/local && \
    cd /usr/local/supervisor-4.0.4 && \
    python setup.py install && \
    rm -rf /tmp/supervisor-4.0.4.tar.gz


COPY supervisord.conf /etc/supervisor/supervisord.conf



# 暴露必要端口
#EXPOSE 9080 9000 2379 9091 9092 9443 80
EXPOSE 80
# 启动命令
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/supervisord.conf"]










