server {
    # 监听 80 端口
    listen 80;
    # 服务器名称
    server_name localhost;

    # 添加详细的日志配置
    access_log /var/log/nginx/access.log;  # 访问日志
    error_log /var/log/nginx/error.log debug;  # 错误日志，级别为 debug

    # 设置根目录和默认索引文件
    root /usr/share/nginx/html;
    index index.html;

    # 处理静态资源（CSS、JS、图片等）
    location /admin/casdoor/static/ {
        # 使用 alias 将请求路径映射到实际文件路径
        alias /opt/casdoor/web/build/static/;
        # 设置资源缓存时间为 1 年
        expires 1y;
        # 设置缓存控制头，允许公共缓存
        add_header Cache-Control "public";
    }

    # 处理 API 请求
    location /api/casdoor/ {
        # 将请求代理到后端服务器
        rewrite ^/api/casdoor/(.*) /$1 break;
        proxy_pass http://127.0.0.1:8000/;
        # 设置代理请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 添加 CORS 头，允许跨域请求
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;  # 预检请求缓存时间
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;  # 返回空响应
        }
    }

    # 处理基础路径下的所有请求
    # location /admin/casdoor/ {
    #    # 使用 alias 将请求路径映射到实际文件路径
    #    alias /opt/casdoor/web/build/;
    #    # 尝试按顺序查找文件，如果都找不到则返回 index.html
    #    try_files $uri $uri/ /casdoor/index.html;

    #    # 设置缓存控制
    #    expires -1;  # 不缓存
    #    add_header Cache-Control "no-store, no-cache, must-revalidate";  # 强制不缓存
    #}
    location /admin/casdoor/ {
        # 使用 alias 必须严格以 / 结尾
        alias /opt/casdoor/web/build/;

        # 修正回退逻辑：所有路由指向 index.html
        try_files $uri $uri/ /admin/casdoor/index.html;

        # 强制不缓存 HTML 文件
        location ~* \.html$ {
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            expires -1;
        }

        # 静态资源缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public";
        }
    }

    # APIsix dashboard配置
    location  / {
        root   /opt/dashboard/web/build/html;
        index  index.html index.htm;
        add_header Access-Control-Allow-Origin *;
    }
    location /admin/apisix {
        alias /opt/dashboard/web/build/html/;  # 确保路径以 / 结尾
        index index.html;
        try_files $uri $uri/ /index.html;

        # 添加必要的权限设置
        autoindex off;
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
        add_header Access-Control-Allow-Headers '*';

        # 缓存配置
        expires 1y;
        add_header Cache-Control "public";
    }
    location /api/apisix/admin {
        # 去掉 /api/ 前缀
        rewrite ^/api/(.*) /$1 break;
        proxy_pass http://127.0.0.1:9000;
        # 设置代理请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 添加 CORS 头，允许跨域请求
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;  # 预检请求缓存时间
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;  # 返回空响应
        }
    }

    location /api/login {
        proxy_pass http://127.0.0.1:8000;
        # 设置代理请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 添加 CORS 头，允许跨域请求
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;  # 预检请求缓存时间
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;  # 返回空响应
        }
    }

    # 处理 eplat 登出请求 - 代理到外部服务
    location /api/eplat/logout {
        # 将 /api/eplat/logout 重写为 /eplat/logout，保留查询参数
        rewrite ^/api/eplat/logout(.*) /eplat/logout$1 break;
        proxy_pass http://eplatxp.baocloud.cn;

        # 设置代理请求头 - 使用目标服务器的Host
        proxy_set_header Host eplatxt.baocloud.cn;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;

        # 代理超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 启用代理缓冲
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;

        # 调试配置 - 记录代理请求的详细信息
        proxy_set_header X-Debug-Original-URI $request_uri;
        proxy_set_header X-Debug-Rewritten-URI $uri;

        # 处理代理错误
        proxy_intercept_errors on;

        # 添加 CORS 头，允许跨域请求
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;  # 预检请求缓存时间
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;  # 返回空响应
        }
    }

    # 处理本地登出请求 - 代理到本地服务
    location /api/logout {
        proxy_pass http://127.0.0.1:8000;
        # 设置代理请求头
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 添加 CORS 头，允许跨域请求
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';

        # 处理 OPTIONS 预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;  # 预检请求缓存时间
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;  # 返回空响应
        }
    }

    # 自定义错误页面
    error_page 404 /casdoor-frontend/404.html;  # 404 错误页面
    error_page 500 502 503 504 /casdoor-frontend/50x.html;  # 5xx 错误页面
}